#!/usr/bin/env python3
"""
Test environmental data scraping from meteo365.es page.
"""

import requests
from bs4 import BeautifulSoup
import re

USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
METEO365_URL = "https://meteo365.es/livecams/malaga.php"

def test_environmental_scraping():
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        response = session.get(METEO365_URL, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        page_text = soup.get_text()
        
        print("=== SEARCHING FOR WEATHER DATA PATTERNS ===")
        
        # Try to find temperature patterns
        temp_matches = re.findall(r'(\d+(?:\.\d+)?)\s*°([CF])', page_text)
        print(f"Temperature patterns found: {temp_matches}")
        
        # Try to find wind speed patterns
        wind_matches = re.findall(r'(\d+(?:\.\d+)?)\s*(km/h|mph|kts|m/s)', page_text)
        print(f"Wind speed patterns found: {wind_matches}")
        
        # Try to find humidity patterns
        humidity_matches = re.findall(r'(\d+(?:\.\d+)?)\s*%', page_text)
        print(f"Humidity patterns found: {humidity_matches}")
        
        # Try to find pressure patterns
        pressure_matches = re.findall(r'(\d+(?:\.\d+)?)\s*(hPa|mbar)', page_text)
        print(f"Pressure patterns found: {pressure_matches}")
        
        # Look for specific weather-related elements
        print("\n=== LOOKING FOR WEATHER ELEMENTS ===")
        
        # Look for elements with IDs that might contain weather data
        weather_ids = ['emtemp', 'emwind', 'emgusts', 'emhum', 'emrain', 'empres']
        for weather_id in weather_ids:
            element = soup.find(id=weather_id)
            if element:
                print(f"Found element #{weather_id}: {element.get_text().strip()}")
            else:
                print(f"Element #{weather_id}: not found")
        
        # Look for weather station section
        weather_station = soup.find(text=re.compile(r'weather station.*Málaga', re.IGNORECASE))
        if weather_station:
            print(f"\nFound weather station section: {weather_station}")
            parent = weather_station.parent
            if parent:
                # Look for nearby data
                for sibling in parent.find_next_siblings()[:5]:
                    if sibling.get_text().strip():
                        print(f"  Sibling text: {sibling.get_text().strip()[:100]}")
        
        # Look for table data
        print("\n=== LOOKING FOR WEATHER TABLES ===")
        tables = soup.find_all('table')
        for i, table in enumerate(tables):
            table_text = table.get_text().strip()
            if any(keyword in table_text.lower() for keyword in ['wind', 'temperature', 'humidity', 'pressure']):
                print(f"Table {i} (weather-related):")
                rows = table.find_all('tr')
                for j, row in enumerate(rows[:3]):  # First 3 rows
                    print(f"  Row {j}: {row.get_text().strip()}")
        
        # Look for any numeric data that might be weather-related
        print("\n=== ALL NUMERIC PATTERNS ===")
        all_numbers = re.findall(r'\d+(?:\.\d+)?', page_text)
        print(f"Found {len(all_numbers)} numeric values in the page")
        
        # Look for specific weather-related text sections
        print("\n=== WEATHER-RELATED TEXT SECTIONS ===")
        weather_keywords = ['wind', 'temperature', 'humidity', 'pressure', 'rain', 'weather']
        for keyword in weather_keywords:
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            if elements:
                print(f"\n'{keyword}' found in {len(elements)} places:")
                for elem in elements[:3]:  # Show first 3
                    parent = elem.parent
                    if parent:
                        context = parent.get_text().strip()[:150]
                        print(f"  Context: {context}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_environmental_scraping()
