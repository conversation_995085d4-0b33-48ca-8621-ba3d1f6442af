#!/usr/bin/env python3
"""
Test script for Selenium-based data collection.

This script demonstrates how to use the selenium_data_collection module
to extract beach cam images and environmental data from JavaScript-rendered pages.
"""

import sys
from pathlib import Path

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from surf_malaga.selenium_data_collection import (
    collect_all_data,
    save_selenium_collection_data,
    collect_meteo365_data,
    collect_tideking_data
)
from datetime import datetime


def test_meteo365_only():
    """Test collecting only meteo365 data."""
    print("=" * 60)
    print("Testing Meteo365 data collection...")
    print("=" * 60)
    
    try:
        data = collect_meteo365_data()
        
        print(f"Collection time: {data.get('collection_time', 'N/A')}")
        print(f"Source: {data.get('source', 'N/A')}")
        
        if 'error' in data:
            print(f"❌ Error: {data['error']}")
            return False
        
        # Check beach cam data
        if 'beach_cam' in data:
            beach_cam = data['beach_cam']
            print(f"✅ Beach cam found:")
            print(f"   - Image URL: {beach_cam.get('image_url', 'N/A')}")
            print(f"   - Image size: {beach_cam.get('image_size_bytes', 0)} bytes")
            print(f"   - Content type: {beach_cam.get('content_type', 'N/A')}")
            print(f"   - Extraction method: {beach_cam.get('extraction_method', 'N/A')}")
        else:
            print("❌ No beach cam data found")
        
        # Check environmental data
        if 'environmental' in data:
            env = data['environmental']
            print(f"✅ Environmental data found:")
            for key, value in env.items():
                print(f"   - {key}: {value}")
        else:
            print("❌ No environmental data found")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception during meteo365 collection: {e}")
        return False


def test_tideking_only():
    """Test collecting only tideking data."""
    print("\n" + "=" * 60)
    print("Testing Tideking data collection...")
    print("=" * 60)
    
    try:
        data = collect_tideking_data()
        
        print(f"Collection time: {data.get('collection_time', 'N/A')}")
        print(f"Source: {data.get('source', 'N/A')}")
        
        if 'error' in data:
            print(f"❌ Error: {data['error']}")
            return False
        
        # Check for any tide-related data
        tide_keys = [k for k in data.keys() if 'tide' in k.lower()]
        if tide_keys:
            print(f"✅ Tide data found:")
            for key in tide_keys:
                print(f"   - {key}: {data[key]}")
        else:
            print("❌ No tide data found (this is expected as extraction is not yet implemented)")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception during tideking collection: {e}")
        return False


def test_full_collection():
    """Test collecting all data and saving to files."""
    print("\n" + "=" * 60)
    print("Testing full data collection and saving...")
    print("=" * 60)
    
    try:
        # Collect all data
        data = collect_all_data()
        
        print(f"Collection time: {data.get('collection_time', 'N/A')}")
        print(f"Collection method: {data.get('collection_method', 'N/A')}")
        
        # Save data
        timestamp = datetime.now()
        saved_paths = save_selenium_collection_data(data, timestamp)
        
        print(f"✅ Data saved successfully:")
        print(f"   - JSON: {saved_paths.get('json_path', 'N/A')}")
        if 'image_path' in saved_paths:
            print(f"   - Image: {saved_paths['image_path']}")
        
        # Print summary
        print(f"\n📊 Collection Summary:")
        print(f"   - Beach cam: {'✅' if 'beach_cam' in data else '❌'}")
        print(f"   - Environmental data: {'✅' if 'environmental' in data else '❌'}")
        print(f"   - Tide data: {'✅' if any(k.startswith('tide') for k in data.keys()) else '❌'}")
        
        if 'meteo365_error' in data:
            print(f"   - Meteo365 error: {data['meteo365_error']}")
        if 'tideking_error' in data:
            print(f"   - Tideking error: {data['tideking_error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception during full collection: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Selenium Data Collection Tests")
    print("This will test extracting beach cam images and environmental data")
    print("from JavaScript-rendered pages using headless Chrome.\n")
    
    # Test individual components
    meteo365_success = test_meteo365_only()
    tideking_success = test_tideking_only()
    full_success = test_full_collection()
    
    # Final summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Meteo365 collection: {'✅ PASS' if meteo365_success else '❌ FAIL'}")
    print(f"Tideking collection: {'✅ PASS' if tideking_success else '❌ FAIL'}")
    print(f"Full collection & save: {'✅ PASS' if full_success else '❌ FAIL'}")
    
    if meteo365_success or tideking_success or full_success:
        print("\n🎉 At least some data collection is working!")
        print("Check the data/external/ directory for saved files.")
    else:
        print("\n⚠️  All tests failed. Check your internet connection and try again.")
        print("You may need to inspect the actual page structure and update the selectors.")


if __name__ == "__main__":
    main()
