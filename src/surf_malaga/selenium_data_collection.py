"""
Selenium-based data collection module for JavaScript-rendered beach cam and environmental data.

This module uses Selenium with headless Chrome to extract data from pages that require
JavaScript rendering, specifically:
- meteo365.es for beach cam images and environmental data
- tideking.com for tide data

The collected data is saved to organized directories with timestamps.
"""

import json
import re
import time
import base64
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from contextlib import contextmanager

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager


# URLs for data collection
METEO365_URL = "https://meteo365.es/livecams/malaga.php"
TIDEKING_URL = "https://tideking.com/Spain/Andalusia/Provincia-de-Malaga/Playa-de-la-Malagueta"


@contextmanager
def get_selenium_driver(headless: bool = True, timeout: int = 30):
    """
    Context manager for Selenium WebDriver with automatic cleanup.

    Args:
        headless: Whether to run browser in headless mode
        timeout: Page load timeout in seconds

    Yields:
        WebDriver instance
    """
    options = Options()

    if headless:
        options.add_argument("--headless=new")  # Use new headless mode

    # Additional options for stability and performance
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-plugins")
    options.add_argument("--disable-web-security")
    options.add_argument("--disable-features=VizDisplayCompositor")
    options.add_argument("--remote-debugging-port=9222")

    # Realistic User-Agent
    options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    # Disable logging to reduce noise
    options.add_argument("--log-level=3")
    options.add_experimental_option("excludeSwitches", ["enable-logging"])
    options.add_experimental_option('useAutomationExtension', False)

    driver = None
    try:
        # Use webdriver-manager to automatically handle ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.set_page_load_timeout(timeout)
        driver.implicitly_wait(10)

        # Execute script to avoid detection
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        yield driver

    except Exception as e:
        print(f"Error creating WebDriver: {e}")
        if driver:
            try:
                driver.quit()
            except:
                pass
        raise
    finally:
        if driver:
            try:
                driver.quit()
            except Exception as e:
                print(f"Error closing WebDriver: {e}")


def collect_meteo365_data() -> Dict[str, Any]:
    """
    Collect beach cam image and environmental data from meteo365.es using Selenium.
    
    Returns:
        Dict containing image data, environmental measurements, and metadata
    """
    result = {
        'collection_time': datetime.now().isoformat(),
        'source': 'meteo365.es',
        'url': METEO365_URL
    }
    
    try:
        with get_selenium_driver() as driver:
            print(f"Loading {METEO365_URL}...")
            driver.get(METEO365_URL)
            
            # Wait for page to load
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Give additional time for JavaScript to render content
            time.sleep(5)
            
            # Extract beach cam image
            beach_cam_data = extract_beach_cam_image(driver)
            if beach_cam_data:
                result['beach_cam'] = beach_cam_data
            
            # Extract environmental data
            env_data = extract_environmental_data(driver)
            if env_data:
                result['environmental'] = env_data
                
            return result
            
    except Exception as e:
        print(f"Error collecting meteo365 data: {e}")
        result['error'] = str(e)
        return result


def extract_beach_cam_image(driver) -> Optional[Dict[str, Any]]:
    """
    Extract beach cam image from the loaded meteo365.es page.
    
    Args:
        driver: Selenium WebDriver instance
        
    Returns:
        Dict containing image data and metadata, or None if extraction fails
    """
    try:
        # Look for various possible image selectors
        image_selectors = [
            "img[src*='webcam']",
            "img[src*='livecam']", 
            "img[src*='malaga']",
            "img[src*='playa']",
            ".webcam img",
            ".livecam img",
            "#webcam img",
            "#livecam img"
        ]
        
        image_element = None
        image_src = None
        
        for selector in image_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    src = element.get_attribute('src')
                    if src and ('webcam' in src.lower() or 'livecam' in src.lower() or 'malaga' in src.lower()):
                        image_element = element
                        image_src = src
                        break
                if image_element:
                    break
            except Exception:
                continue
        
        if not image_element or not image_src:
            print("No beach cam image found")
            return None
            
        # Get image as base64 data
        try:
            # Try to get image data directly from the element
            image_data_b64 = driver.execute_script("""
                var canvas = document.createElement('canvas');
                var ctx = canvas.getContext('2d');
                var img = arguments[0];
                canvas.width = img.naturalWidth || img.width;
                canvas.height = img.naturalHeight || img.height;
                ctx.drawImage(img, 0, 0);
                return canvas.toDataURL('image/jpeg', 0.8);
            """, image_element)
            
            if image_data_b64 and image_data_b64.startswith('data:image'):
                # Extract base64 data
                image_data = image_data_b64.split(',')[1]
                image_bytes = base64.b64decode(image_data)
                
                return {
                    'image_url': image_src,
                    'image_size_bytes': len(image_bytes),
                    'content_type': 'image/jpeg',
                    'extraction_method': 'canvas_screenshot',
                    '_image_data': image_bytes
                }
        except Exception as e:
            print(f"Canvas method failed: {e}")
        
        # Fallback: try to download the image URL directly
        try:
            import requests
            response = requests.get(image_src, timeout=10)
            response.raise_for_status()
            
            return {
                'image_url': image_src,
                'image_size_bytes': len(response.content),
                'content_type': response.headers.get('content-type', 'image/jpeg'),
                'extraction_method': 'direct_download',
                '_image_data': response.content
            }
        except Exception as e:
            print(f"Direct download failed: {e}")
            
        return None
        
    except Exception as e:
        print(f"Error extracting beach cam image: {e}")
        return None


def extract_environmental_data(driver) -> Optional[Dict[str, Any]]:
    """
    Extract environmental data (wind, temperature, etc.) from the loaded meteo365.es page.
    
    Args:
        driver: Selenium WebDriver instance
        
    Returns:
        Dict containing environmental measurements, or None if extraction fails
    """
    try:
        env_data = {}
        
        # Look for temperature data
        temp_selectors = [
            "*[class*='temp']",
            "*[class*='temperature']", 
            "*[id*='temp']",
            "*[id*='temperature']"
        ]
        
        for selector in temp_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.strip()
                    # Look for temperature pattern (number followed by °C or °)
                    temp_match = re.search(r'(-?\d+(?:\.\d+)?)\s*°[CF]?', text)
                    if temp_match:
                        env_data['temperature_c'] = float(temp_match.group(1))
                        break
                if 'temperature_c' in env_data:
                    break
            except Exception:
                continue
        
        # Look for wind data
        wind_selectors = [
            "*[class*='wind']",
            "*[class*='viento']",
            "*[id*='wind']",
            "*[id*='viento']"
        ]
        
        for selector in wind_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.strip()
                    # Look for wind speed pattern
                    wind_match = re.search(r'(\d+(?:\.\d+)?)\s*(km/h|mph|knots|kts)', text, re.IGNORECASE)
                    if wind_match:
                        speed = float(wind_match.group(1))
                        unit = wind_match.group(2).lower()
                        
                        # Convert to knots
                        if 'km' in unit:
                            speed = speed * 0.539957  # km/h to knots
                        elif 'mph' in unit:
                            speed = speed * 0.868976  # mph to knots
                        
                        env_data['wind_speed_knots'] = speed
                        break
                if 'wind_speed_knots' in env_data:
                    break
            except Exception:
                continue
        
        # Look for wave data
        wave_selectors = [
            "*[class*='wave']",
            "*[class*='ola']",
            "*[id*='wave']",
            "*[id*='ola']"
        ]
        
        for selector in wave_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.strip()
                    # Look for wave height pattern
                    wave_match = re.search(r'(\d+(?:\.\d+)?)\s*m', text)
                    if wave_match:
                        env_data['wave_height_m'] = float(wave_match.group(1))
                        break
                if 'wave_height_m' in env_data:
                    break
            except Exception:
                continue
        
        return env_data if env_data else None
        
    except Exception as e:
        print(f"Error extracting environmental data: {e}")
        return None


def collect_tideking_data() -> Dict[str, Any]:
    """
    Collect tide data from tideking.com using Selenium.
    
    Returns:
        Dict containing tide information and interpolated current height
    """
    result = {
        'collection_time': datetime.now().isoformat(),
        'source': 'tideking.com',
        'url': TIDEKING_URL
    }
    
    try:
        with get_selenium_driver() as driver:
            print(f"Loading {TIDEKING_URL}...")
            driver.get(TIDEKING_URL)
            
            # Wait for page to load
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Give additional time for JavaScript to render content
            time.sleep(5)
            
            # Extract tide data
            tide_data = extract_tide_data(driver)
            if tide_data:
                result.update(tide_data)
                
            return result
            
    except Exception as e:
        print(f"Error collecting tideking data: {e}")
        result['error'] = str(e)
        return result


def extract_tide_data(driver) -> Optional[Dict[str, Any]]:
    """
    Extract tide data from the loaded tideking.com page.

    Args:
        driver: Selenium WebDriver instance

    Returns:
        Dict containing tide information, or None if extraction fails
    """
    try:
        # This would need to be implemented based on the actual structure
        # of the tideking.com page after JavaScript rendering
        # For now, return None to indicate this needs further development
        print("Tide data extraction from JavaScript-rendered page not yet implemented")
        return None

    except Exception as e:
        print(f"Error extracting tide data: {e}")
        return None


def collect_all_data() -> Dict[str, Any]:
    """
    Collect all available data using Selenium.

    Returns:
        Dict containing all collected data
    """
    timestamp = datetime.now()

    result = {
        'collection_time': timestamp.isoformat(),
        'collection_method': 'selenium'
    }

    # Collect meteo365 data (beach cam + environmental)
    print("Collecting meteo365 data...")
    meteo365_data = collect_meteo365_data()
    if 'error' not in meteo365_data:
        if 'beach_cam' in meteo365_data:
            result['beach_cam'] = meteo365_data['beach_cam']
        if 'environmental' in meteo365_data:
            result['environmental'] = meteo365_data['environmental']
    else:
        result['meteo365_error'] = meteo365_data['error']

    # Collect tideking data
    print("Collecting tideking data...")
    tideking_data = collect_tideking_data()
    if 'error' not in tideking_data:
        # Extract any tide-specific data
        for key, value in tideking_data.items():
            if key not in ['collection_time', 'source', 'url']:
                result[key] = value
    else:
        result['tideking_error'] = tideking_data['error']

    return result


def save_selenium_collection_data(data: Dict[str, Any], timestamp: datetime) -> Dict[str, str]:
    """
    Save collected data to organized directory structure.

    Args:
        data: Combined data dictionary to save as JSON
        timestamp: Collection timestamp

    Returns:
        Dict with paths where data was saved
    """
    # Create directory structure: data/external/year/month/day
    base_dir = Path("data/external")
    date_dir = base_dir / str(timestamp.year) / f"{timestamp.month:02d}" / f"{timestamp.day:02d}"
    date_dir.mkdir(parents=True, exist_ok=True)

    # Create filename with timestamp
    timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")

    saved_paths = {}

    # Extract and save image data if available
    image_data = None
    if 'beach_cam' in data and '_image_data' in data['beach_cam']:
        image_data = data['beach_cam']['_image_data']

        # Determine file extension based on content type
        content_type = data['beach_cam'].get('content_type', 'image/jpeg')
        if 'webp' in content_type:
            image_filename = f"{timestamp_str}_selenium.webp"
        elif 'png' in content_type:
            image_filename = f"{timestamp_str}_selenium.png"
        else:
            image_filename = f"{timestamp_str}_selenium.jpg"

        image_path = date_dir / image_filename
        with open(image_path, 'wb') as f:
            f.write(image_data)
        saved_paths['image_path'] = str(image_path)
        saved_paths['image_filename'] = image_filename

        # Update the data with the image filename and remove binary data
        data['beach_cam']['image_saved'] = image_filename
        del data['beach_cam']['_image_data']

    # Save JSON data
    json_path = date_dir / f"{timestamp_str}_selenium.json"
    with open(json_path, 'w') as f:
        json.dump(data, f, indent=2)
    saved_paths['json_path'] = str(json_path)

    return saved_paths


if __name__ == "__main__":
    """
    Example usage: collect all data and save to files.
    """
    print("Starting Selenium-based data collection...")

    # Collect all data
    data = collect_all_data()

    # Save data
    timestamp = datetime.now()
    saved_paths = save_selenium_collection_data(data, timestamp)

    print(f"Data collection completed!")
    print(f"JSON saved to: {saved_paths.get('json_path', 'N/A')}")
    if 'image_path' in saved_paths:
        print(f"Image saved to: {saved_paths['image_path']}")

    # Print summary
    print("\nCollection Summary:")
    print(f"- Collection time: {data['collection_time']}")
    print(f"- Beach cam: {'✓' if 'beach_cam' in data else '✗'}")
    print(f"- Environmental data: {'✓' if 'environmental' in data else '✗'}")
    print(f"- Tide data: {'✓' if any(k.startswith('tide') for k in data.keys()) else '✗'}")

    if 'meteo365_error' in data:
        print(f"- Meteo365 error: {data['meteo365_error']}")
    if 'tideking_error' in data:
        print(f"- Tideking error: {data['tideking_error']}")
