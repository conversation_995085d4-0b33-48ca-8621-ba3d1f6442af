"""
Real-time data collection module for beach cam images, environmental data, and tide data.

This module provides functions to scrape data from:
- meteo365.es for beach cam images and environmental data
- tideking.com for tide data

The collected data is saved to organized directories with timestamps.
"""

import requests
import json
import re
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup
import pandas as pd


# Realistic browser User-Agent to avoid being blocked
USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# URLs for data collection
METEO365_URL = "https://meteo365.es/livecams/malaga.php"
TIDEKING_URL = "https://tideking.com/Spain/Andalusia/Provincia-de-Malaga/Playa-de-la-Malagueta"


def get_session() -> requests.Session:
    """Create a requests session with realistic headers."""
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    return session


def collect_beach_cam_data() -> Dict[str, Any]:
    """
    Collect beach cam image from meteo365.es.

    Returns:
        Dict containing image URL, image data, and any metadata
        Note: image_data is returned separately to avoid JSON serialization issues
    """
    session = get_session()

    # Try multiple webcam URLs in order of preference
    webcam_urls = [
        "https://webcam.meteo365.es/livecams/playa/current.webp",  # Live webcam (if available)
        "https://meteo365.es/livecams/thumbs/cams/malagaplaya.jpg"  # Fallback thumbnail
    ]

    for image_url in webcam_urls:
        try:
            # Add referer header for the live webcam
            headers = {}
            if 'webcam.meteo365.es' in image_url:
                headers['Referer'] = 'https://meteo365.es/livecams/malaga.php'
                headers['Accept'] = 'image/webp,image/apng,image/*,*/*;q=0.8'

            # Download the image
            img_response = session.get(image_url, timeout=30, headers=headers)
            img_response.raise_for_status()

            # Verify it's actually an image
            content_type = img_response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                print(f"URL {image_url} did not return an image: content-type={content_type}")
                continue

            image_data = img_response.content
            image_size = len(image_data)

            # Return metadata without the binary data to avoid JSON serialization issues
            result = {
                'image_url': image_url,
                'image_size_bytes': image_size,
                'content_type': content_type,
                'collection_time': datetime.now().isoformat(),
                'source': 'meteo365.es',
                'camera_location': 'Malaga Beach La Malagueta',
                'is_live_feed': 'webcam.meteo365.es' in image_url
            }

            # Store image data separately for saving to file
            result['_image_data'] = image_data

            return result

        except Exception as e:
            print(f"Error with webcam URL {image_url}: {e}")
            continue

    # If all URLs failed
    return {
        'error': 'All webcam URLs failed',
        'collection_time': datetime.now().isoformat(),
        'source': 'meteo365.es'
    }


def collect_environmental_data() -> Dict[str, Any]:
    """
    Collect environmental data (wind, temperature, etc.) from meteo365.es.

    Returns:
        Dict containing environmental measurements
    """
    session = get_session()

    env_data = {
        'collection_time': datetime.now().isoformat(),
        'source': 'meteo365.es'
    }

    try:
        # First try the direct weather data URL
        raise NotImplementedError("Direct weather data URL not implemented")

    except Exception as e:
        print(f"Error collecting environmental data: {e}")
        env_data['error'] = str(e)
        return env_data


def collect_tide_data() -> Dict[str, Any]:
    """
    Collect current tide data from tideking.com and interpolate current height.
    Only collects today's tide data from the first row of the tide table.

    Returns:
        Dict containing tide information and interpolated current height
    """
    session = get_session()

    try:
        response = session.get(TIDEKING_URL, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        tide_data = {
            'collection_time': datetime.now().isoformat(),
            'source': 'tideking.com'
        }

        # Look for the tide table with title "Tide table for Playa de la Malagueta"
        # We want only the first data row (today's tides)

        today_tides = []

        # Find the table with tide data
        # Look for rows that contain day names and tide times
        for row in soup.find_all('tr'):
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 5:  # Day + 4 tide times + sunrise/set
                # Check if this row contains today's data
                # Look for patterns like "Wed 16" or day names
                first_cell_text = cells[0].get_text().strip()

                # If this looks like a day row (contains day abbreviation)
                if any(day in first_cell_text for day in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']):
                    # Extract tide times from this row (cells 1-4)
                    for i in range(1, min(5, len(cells))):  # Up to 4 tide times
                        cell_text = cells[i].get_text().strip()

                        # Look for time and height pattern
                        lines = cell_text.split('\n')
                        time_str = None
                        height_str = None
                        tide_type = None

                        for line in lines:
                            line = line.strip()
                            # Look for time pattern
                            time_match = re.search(r'(\d{1,2}):(\d{2})\s*(am|pm)', line)
                            if time_match:
                                hour = int(time_match.group(1))
                                minute = int(time_match.group(2))
                                period = time_match.group(3)

                                # Convert to 24-hour format
                                if period == 'pm' and hour != 12:
                                    hour += 12
                                elif period == 'am' and hour == 12:
                                    hour = 0

                                time_str = f"{hour:02d}:{minute:02d}"

                            # Look for height pattern
                            height_match = re.search(r'([+-]?\d+(?:\.\d+)?)\s*m', line)
                            if height_match:
                                height_str = float(height_match.group(1))

                            # Determine tide type
                            if '▲' in line:
                                tide_type = 'high'
                            elif '▼' in line:
                                tide_type = 'low'

                        # If we found both time and height, add to today's tides
                        if time_str and height_str is not None:
                            today_tides.append({
                                'time': time_str,
                                'height_m': height_str,
                                'type': tide_type or 'unknown'
                            })

                    # We only want the first (today's) row, so break after processing it
                    if today_tides:
                        break

        tide_data['todays_tides'] = today_tides

        # Interpolate current tide height using only today's tides
        if len(today_tides) >= 2:
            current_height = interpolate_tide_height(today_tides)
            tide_data['current_height_m'] = current_height

        return tide_data

    except Exception as e:
        print(f"Error collecting tide data: {e}")
        return {
            'error': str(e),
            'collection_time': datetime.now().isoformat(),
            'source': 'tideking.com'
        }


def interpolate_tide_height(tide_times: list) -> Optional[float]:
    """
    Interpolate current tide height based on tide times and heights.
    
    Args:
        tide_times: List of dicts with 'time' and 'height_m' keys
        
    Returns:
        Interpolated tide height in meters, or None if interpolation fails
    """
    if len(tide_times) < 2:
        return None
    
    try:
        current_time = datetime.now()
        current_minutes = current_time.hour * 60 + current_time.minute
        
        # Convert tide times to minutes since midnight
        tide_points = []
        for tide in tide_times:
            time_parts = tide['time'].split(':')
            tide_minutes = int(time_parts[0]) * 60 + int(time_parts[1])
            tide_points.append((tide_minutes, tide['height_m']))
        
        # Sort by time
        tide_points.sort()
        
        # Find the two tide points to interpolate between
        before_point = None
        after_point = None
        
        for time_min, height in tide_points:
            if time_min <= current_minutes:
                before_point = (time_min, height)
            else:
                after_point = (time_min, height)
                break
        
        # If we're after all tide times, use the last two points
        if after_point is None and len(tide_points) >= 2:
            before_point = tide_points[-2]
            after_point = tide_points[-1]
            # Extrapolate forward
            time_diff = after_point[0] - before_point[0]
            height_diff = after_point[1] - before_point[1]
            time_since_last = current_minutes - after_point[0]
            if time_since_last > 0:
                slope = height_diff / time_diff if time_diff != 0 else 0
                return after_point[1] + slope * time_since_last
        
        # If we're before all tide times, use the first two points
        if before_point is None and len(tide_points) >= 2:
            before_point = tide_points[0]
            after_point = tide_points[1]
            # Extrapolate backward
            time_diff = after_point[0] - before_point[0]
            height_diff = after_point[1] - before_point[1]
            time_before_first = before_point[0] - current_minutes
            if time_before_first > 0:
                slope = height_diff / time_diff if time_diff != 0 else 0
                return before_point[1] - slope * time_before_first
        
        # Normal interpolation between two points
        if before_point and after_point:
            time_diff = after_point[0] - before_point[0]
            height_diff = after_point[1] - before_point[1]
            time_progress = (current_minutes - before_point[0]) / time_diff if time_diff != 0 else 0
            return before_point[1] + height_diff * time_progress
        
        return None
        
    except Exception as e:
        print(f"Error interpolating tide height: {e}")
        return None


def save_collection_data(data: Dict[str, Any], image_data: Optional[bytes],
                        timestamp: datetime) -> Dict[str, str]:
    """
    Save collected data to organized directory structure.

    Args:
        data: Combined data dictionary to save as JSON
        image_data: Beach cam image data (bytes)
        timestamp: Collection timestamp

    Returns:
        Dict with paths where data was saved
    """
    # Create directory structure: data/external/year/month/day
    base_dir = Path("data/external")
    date_dir = base_dir / str(timestamp.year) / f"{timestamp.month:02d}" / f"{timestamp.day:02d}"
    date_dir.mkdir(parents=True, exist_ok=True)

    # Create filename with timestamp
    timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")

    saved_paths = {}

    # Save image first if available
    if image_data:
        # Determine file extension based on content type or default to jpg
        image_filename = f"{timestamp_str}.jpg"
        if 'beach_cam' in data and 'content_type' in data['beach_cam']:
            content_type = data['beach_cam']['content_type']
            if 'webp' in content_type:
                image_filename = f"{timestamp_str}.webp"
            elif 'png' in content_type:
                image_filename = f"{timestamp_str}.png"

        image_path = date_dir / image_filename
        with open(image_path, 'wb') as f:
            f.write(image_data)
        saved_paths['image_path'] = str(image_path)
        saved_paths['image_filename'] = image_filename

        # Update the data with the image filename
        if 'beach_cam' in data:
            data['beach_cam']['image_saved'] = image_filename
    else:
        # No image data
        if 'beach_cam' in data:
            data['beach_cam']['image_saved'] = False

    # Save JSON data (after updating image_saved)
    json_path = date_dir / f"{timestamp_str}.json"
    with open(json_path, 'w') as f:
        json.dump(data, f, indent=2)
    saved_paths['json_path'] = str(json_path)

    return saved_paths
