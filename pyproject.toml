[project]
name = "surf-malaga"
version = "0.1.0"
description = "Add your description here"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
dependencies = [
    "jupyter>=1.1.1",
    "pandas>=2.3.1",
    "numpy>=2.3.1",
    "matplotlib>=3.10.3",
    "seaborn>=0.13.2",
    "scikit-learn>=1.7.0",
    "plotly>=6.2.0",
    "beautifulsoup4>=4.13.4",
    "lxml>=6.0.0",
    "pyarrow>=20.0.0",
    "pdfplumber>=0.11.7",
    "jupyter-contrib-nbextensions>=0.7.0",
    "requests>=2.32.4",
    "selenium>=4.34.2",
    "webdriver-manager>=4.0.2",
]
readme = "README.md"
requires-python = ">= 3.8"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = []

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["src/surf_malaga"]
