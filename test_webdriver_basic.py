#!/usr/bin/env python3
"""
Basic WebDriver test to check if Selenium is working properly.
"""

import sys
from pathlib import Path

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from surf_malaga.selenium_data_collection import get_selenium_driver


def test_basic_webdriver():
    """Test basic WebDriver functionality."""
    print("Testing basic WebDriver functionality...")
    
    try:
        with get_selenium_driver(headless=True) as driver:
            print("✅ WebDriver created successfully")
            
            # Test basic navigation
            print("🔄 Testing navigation to Google...")
            driver.get("https://www.google.com")
            
            title = driver.title
            print(f"✅ Page loaded successfully. Title: {title}")
            
            # Test finding an element
            search_box = driver.find_element("name", "q")
            print("✅ Found search box element")
            
            return True
            
    except Exception as e:
        print(f"❌ WebDriver test failed: {e}")
        return False


def test_target_sites():
    """Test accessing the target sites without extraction."""
    print("\nTesting access to target sites...")
    
    sites = [
        ("Meteo365", "https://meteo365.es/livecams/malaga.php"),
        ("TideKing", "https://tideking.com/Spain/Andalusia/Provincia-de-Malaga/Playa-de-la-Malagueta")
    ]
    
    results = {}
    
    for site_name, url in sites:
        print(f"\n🔄 Testing {site_name}: {url}")
        
        try:
            with get_selenium_driver(headless=True, timeout=60) as driver:
                driver.get(url)
                
                title = driver.title
                page_source_length = len(driver.page_source)
                
                print(f"✅ {site_name} loaded successfully")
                print(f"   Title: {title}")
                print(f"   Page source length: {page_source_length} characters")
                
                results[site_name] = {
                    'success': True,
                    'title': title,
                    'page_length': page_source_length
                }
                
        except Exception as e:
            print(f"❌ {site_name} failed: {e}")
            results[site_name] = {
                'success': False,
                'error': str(e)
            }
    
    return results


def main():
    """Run all basic tests."""
    print("🚀 Starting Basic WebDriver Tests")
    print("=" * 50)
    
    # Test basic WebDriver functionality
    basic_success = test_basic_webdriver()
    
    # Test target sites
    site_results = test_target_sites()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    print(f"Basic WebDriver: {'✅ PASS' if basic_success else '❌ FAIL'}")
    
    for site_name, result in site_results.items():
        status = '✅ PASS' if result['success'] else '❌ FAIL'
        print(f"{site_name}: {status}")
        if not result['success']:
            print(f"   Error: {result['error']}")
    
    if basic_success and all(r['success'] for r in site_results.values()):
        print("\n🎉 All tests passed! WebDriver is working correctly.")
        print("You can now proceed with data extraction.")
    elif basic_success:
        print("\n⚠️  WebDriver works, but some target sites failed.")
        print("Check your internet connection or site availability.")
    else:
        print("\n❌ WebDriver setup failed. Check your Chrome installation.")


if __name__ == "__main__":
    main()
