#!/usr/bin/env python3
"""
Test extracting weather data from tideking.com page.
"""

import requests
from bs4 import BeautifulSoup
import re

USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
TIDEKING_URL = "https://tideking.com/Spain/Andalusia/Provincia-de-Malaga/Playa-de-la-Malagueta"

def test_tideking_weather():
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        response = session.get(TIDEKING_URL, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print("=== EXTRACTING WEATHER DATA FROM TIDEKING ===")
        
        weather_data = {}
        
        # Look for current weather section
        weather_section = soup.find(text=re.compile(r'Current weather', re.IGNORECASE))
        if weather_section:
            print("Found current weather section")
            
            # Find the parent container
            parent = weather_section.parent
            while parent and parent.name != 'div':
                parent = parent.parent
            
            if parent:
                # Look for temperature
                temp_elements = parent.find_all(text=re.compile(r'\d+°C'))
                for temp in temp_elements:
                    temp_match = re.search(r'(\d+)°C', temp)
                    if temp_match:
                        temp_value = int(temp_match.group(1))
                        # Determine if it's min or max based on context
                        context = temp.parent.get_text() if temp.parent else ""
                        if 'min' in context.lower():
                            weather_data['temperature_min_c'] = temp_value
                        elif 'max' in context.lower():
                            weather_data['temperature_max_c'] = temp_value
                        else:
                            weather_data['temperature_c'] = temp_value
                        print(f"Temperature: {temp_value}°C ({context.strip()})")
                
                # Look for wind data
                wind_elements = parent.find_all(text=re.compile(r'\d+\s*km/h'))
                for wind in wind_elements:
                    wind_match = re.search(r'(\d+)\s*km/h', wind)
                    if wind_match:
                        wind_value = int(wind_match.group(1))
                        context = wind.parent.get_text() if wind.parent else ""
                        if 'gust' in context.lower():
                            weather_data['wind_gust_kmh'] = wind_value
                        else:
                            weather_data['wind_speed_kmh'] = wind_value
                        print(f"Wind: {wind_value} km/h ({context.strip()})")
                
                # Look for humidity
                humidity_elements = parent.find_all(text=re.compile(r'\d+%'))
                for humidity in humidity_elements:
                    humidity_match = re.search(r'(\d+)%', humidity)
                    if humidity_match:
                        humidity_value = int(humidity_match.group(1))
                        weather_data['humidity_percent'] = humidity_value
                        print(f"Humidity: {humidity_value}%")
                
                # Look for weather description
                weather_desc_elements = parent.find_all(['h5', 'h4', 'h3'])
                for desc in weather_desc_elements:
                    desc_text = desc.get_text().strip()
                    if desc_text and desc_text not in ['Weather', 'Temperature', 'Wind', 'Humidity']:
                        weather_data['weather_description'] = desc_text
                        print(f"Weather description: {desc_text}")
        
        # Look for water temperature
        water_temp_text = soup.find(text=re.compile(r'water temperature.*\d+°C', re.IGNORECASE))
        if water_temp_text:
            water_temp_match = re.search(r'(\d+)°C', water_temp_text)
            if water_temp_match:
                weather_data['water_temperature_c'] = int(water_temp_match.group(1))
                print(f"Water temperature: {water_temp_match.group(1)}°C")
        
        print(f"\nExtracted weather data: {weather_data}")
        return weather_data
        
    except Exception as e:
        print(f"Error: {e}")
        return {}

if __name__ == "__main__":
    test_tideking_weather()
