{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Selenium-Based Data Collection Demo\n", "\n", "This notebook demonstrates how to use Selenium with headless Chrome to extract beach cam images and environmental data from JavaScript-rendered pages.\n", "\n", "## Target Sources\n", "- **meteo365.es**: Beach cam images and environmental data (wind, temperature)\n", "- **tideking.com**: Tide data\n", "\n", "## Setup\n", "Make sure you have installed the required dependencies:\n", "```bash\n", "rye add selenium webdriver-manager\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "import base64\n", "from IPython.display import Image, display, HTML\n", "\n", "# Add src to path\n", "sys.path.insert(0, str(Path.cwd().parent / \"src\"))\n", "\n", "from surf_malaga.selenium_data_collection import (\n", "    collect_meteo365_data,\n", "    collect_tideking_data,\n", "    collect_all_data,\n", "    save_selenium_collection_data\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 1: Collect Meteo365 Data (Beach Cam + Environmental)\n", "\n", "This will attempt to extract:\n", "- Beach cam image from the live webcam\n", "- Environmental data (temperature, wind speed, wave height)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"🔄 Collecting data from meteo365.es...\")\n", "print(\"This may take 30-60 seconds as we load the page and wait for JavaScript rendering.\")\n", "\n", "meteo365_data = collect_meteo365_data()\n", "\n", "print(\"\\n📊 Results:\")\n", "print(json.dumps({\n", "    k: v for k, v in meteo365_data.items() \n", "    if k != '_image_data' and not (isinstance(v, dict) and '_image_data' in v)\n", "}, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Display Beach Cam Image (if found)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'beach_cam' in meteo365_data and '_image_data' in meteo365_data['beach_cam']:\n", "    image_data = meteo365_data['beach_cam']['_image_data']\n", "    \n", "    print(f\"🖼️ Beach cam image found!\")\n", "    print(f\"   Size: {len(image_data)} bytes\")\n", "    print(f\"   URL: {meteo365_data['beach_cam'].get('image_url', 'N/A')}\")\n", "    print(f\"   Method: {meteo365_data['beach_cam'].get('extraction_method', 'N/A')}\")\n", "    \n", "    # Display the image\n", "    display(Image(data=image_data))\n", "else:\n", "    print(\"❌ No beach cam image found\")\n", "    if 'error' in meteo365_data:\n", "        print(f\"Error: {meteo365_data['error']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Display Environmental Data (if found)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'environmental' in meteo365_data:\n", "    env_data = meteo365_data['environmental']\n", "    \n", "    print(\"🌡️ Environmental data found:\")\n", "    \n", "    # Create a nice display table\n", "    html_table = \"<table style='border-collapse: collapse; width: 100%;'>\"\n", "    html_table += \"<tr style='background-color: #f2f2f2;'><th style='border: 1px solid #ddd; padding: 8px;'>Measurement</th><th style='border: 1px solid #ddd; padding: 8px;'>Value</th></tr>\"\n", "    \n", "    for key, value in env_data.items():\n", "        # Format the key nicely\n", "        display_key = key.replace('_', ' ').title()\n", "        html_table += f\"<tr><td style='border: 1px solid #ddd; padding: 8px;'>{display_key}</td><td style='border: 1px solid #ddd; padding: 8px;'>{value}</td></tr>\"\n", "    \n", "    html_table += \"</table>\"\n", "    display(HTML(html_table))\n", "else:\n", "    print(\"❌ No environmental data found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 2: Collect Tideking Data\n", "\n", "This will attempt to extract tide information from tideking.com."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"🔄 Collecting data from tideking.com...\")\n", "print(\"This may take 30-60 seconds as we load the page and wait for JavaScript rendering.\")\n", "\n", "tideking_data = collect_tideking_data()\n", "\n", "print(\"\\n📊 Results:\")\n", "print(json.dumps(tideking_data, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 3: Collect All Data and Save\n", "\n", "This will collect data from all sources and save it to the organized directory structure."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"🔄 Collecting all data...\")\n", "print(\"This may take 1-2 minutes as we visit multiple pages.\")\n", "\n", "all_data = collect_all_data()\n", "\n", "print(\"\\n💾 Saving data to files...\")\n", "timestamp = datetime.now()\n", "saved_paths = save_selenium_collection_data(all_data, timestamp)\n", "\n", "print(\"\\n✅ Data collection and saving completed!\")\n", "print(f\"📁 Files saved:\")\n", "for key, path in saved_paths.items():\n", "    print(f\"   {key}: {path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary of Collected Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"📊 Collection Summary:\")\n", "print(f\"   Collection time: {all_data.get('collection_time', 'N/A')}\")\n", "print(f\"   Collection method: {all_data.get('collection_method', 'N/A')}\")\n", "print(f\"   Beach cam: {'✅' if 'beach_cam' in all_data else '❌'}\")\n", "print(f\"   Environmental data: {'✅' if 'environmental' in all_data else '❌'}\")\n", "print(f\"   Tide data: {'✅' if any(k.startswith('tide') for k in all_data.keys()) else '❌'}\")\n", "\n", "if 'meteo365_error' in all_data:\n", "    print(f\"   ⚠️ Meteo365 error: {all_data['meteo365_error']}\")\n", "if 'tideking_error' in all_data:\n", "    print(f\"   ⚠️ Tideking error: {all_data['tideking_error']}\")\n", "\n", "# Display the final data structure (without binary image data)\n", "display_data = {\n", "    k: v for k, v in all_data.items() \n", "    if k != '_image_data' and not (isinstance(v, dict) and '_image_data' in v)\n", "}\n", "\n", "print(\"\\n📋 Final data structure:\")\n", "print(json.dumps(display_data, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Steps\n", "\n", "1. **Inspect Page Structure**: If data extraction is not working, you may need to:\n", "   - Visit the target pages manually\n", "   - Inspect the HTML structure after JavaScript rendering\n", "   - Update the CSS selectors in the extraction functions\n", "\n", "2. **Improve Extraction Logic**: The current implementation uses generic selectors. You may need to:\n", "   - Add more specific selectors for each data type\n", "   - Handle different page layouts or data formats\n", "   - Add retry logic for failed extractions\n", "\n", "3. **Schedule Regular Collection**: Once working, you can:\n", "   - Set up a cron job to run collection periodically\n", "   - Build a dataset of image-environment pairs over time\n", "   - Use this data for training surf prediction models\n", "\n", "4. **Error <PERSON>**: Add more robust error handling for:\n", "   - Network timeouts\n", "   - Page structure changes\n", "   - WebDriver issues"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}