{"collection_time": "2025-07-16T16:56:49.598413", "collection_method": "selenium", "meteo365_error": "Message: no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.101)\nStacktrace:\n0   chromedriver                        0x0000000110299a28 chromedriver + 6167080\n1   chromedriver                        0x0000000110290fda chromedriver + 6131674\n2   chromedriver                        0x000000010fd1de00 chromedriver + 417280\n3   chromedriver                        0x000000010fcf27c0 chromedriver + 239552\n4   chromedriver                        0x000000010fd9de68 chromedriver + 941672\n5   chromedriver                        0x000000010fdbcabc chromedriver + 1067708\n6   chromedriver                        0x000000010fd95a63 chromedriver + 907875\n7   chromedriver                        0x000000010fd620c7 chromedriver + 696519\n8   chromedriver                        0x000000010fd62d31 chromedriver + 699697\n9   chromedriver                        0x0000000110257be0 chromedriver + 5897184\n10  chromedriver                        0x000000011025bc59 chromedriver + 5913689\n11  chromedriver                        0x00000001102328f2 chromedriver + 5744882\n12  chromedriver                        0x000000011025c5df chromedriver + 5916127\n13  chromedriver                        0x00000001102215c4 chromedriver + 5674436\n14  chromedriver                        0x000000011027e5b8 chromedriver + 6055352\n15  chromedriver                        0x000000011027e780 chromedriver + 6055808\n16  chromedriver                        0x0000000110290b71 chromedriver + 6130545\n17  libsystem_pthread.dylib             0x00007ff80c357df1 _pthread_start + 99\n18  libsystem_pthread.dylib             0x00007ff80c353857 thread_start + 15\n", "tideking_error": "Message: no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.101)\nStacktrace:\n0   chromedriver                        0x000000010a6f0a28 chromedriver + 6167080\n1   chromedriver                        0x000000010a6e7fda chromedriver + 6131674\n2   chromedriver                        0x000000010a174e00 chromedriver + 417280\n3   chromedriver                        0x000000010a1497c0 chromedriver + 239552\n4   chromedriver                        0x000000010a1f4e68 chromedriver + 941672\n5   chromedriver                        0x000000010a213abc chromedriver + 1067708\n6   chromedriver                        0x000000010a1eca63 chromedriver + 907875\n7   chromedriver                        0x000000010a1b90c7 chromedriver + 696519\n8   chromedriver                        0x000000010a1b9d31 chromedriver + 699697\n9   chromedriver                        0x000000010a6aebe0 chromedriver + 5897184\n10  chromedriver                        0x000000010a6b2c59 chromedriver + 5913689\n11  chromedriver                        0x000000010a6898f2 chromedriver + 5744882\n12  chromedriver                        0x000000010a6b35df chromedriver + 5916127\n13  chromedriver                        0x000000010a6785c4 chromedriver + 5674436\n14  chromedriver                        0x000000010a6d55b8 chromedriver + 6055352\n15  chromedriver                        0x000000010a6d5780 chromedriver + 6055808\n16  chromedriver                        0x000000010a6e7b71 chromedriver + 6130545\n17  libsystem_pthread.dylib             0x00007ff80c357df1 _pthread_start + 99\n18  libsystem_pthread.dylib             0x00007ff80c353857 thread_start + 15\n"}